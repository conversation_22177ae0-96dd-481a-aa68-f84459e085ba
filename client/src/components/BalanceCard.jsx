import React from 'react'

export default function BalanceCard({ balance }){
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0)
  }

  const getBalanceColor = (balance) => {
    if (balance > 0) return '#4caf50'
    if (balance < 0) return '#f44336'
    return '#666'
  }

  const totalIn = balance.total_in || 0
  const totalOut = balance.total_out || 0
  const currentBalance = balance.balance || 0

  return (
    <div className="card balance">
      <div>
        <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>💰 Entrées</div>
        <div className="big" style={{ color: '#4caf50' }}>
          {formatCurrency(totalIn)}
        </div>
      </div>
      <div>
        <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>💸 Dépenses</div>
        <div className="big" style={{ color: '#f44336' }}>
          {formatCurrency(totalOut)}
        </div>
      </div>
      <div>
        <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>
          {currentBalance >= 0 ? '✅' : '⚠️'} Solde
        </div>
        <div className="big" style={{ color: getBalanceColor(currentBalance) }}>
          {formatCurrency(currentBalance)}
        </div>
      </div>
    </div>
  )
}
