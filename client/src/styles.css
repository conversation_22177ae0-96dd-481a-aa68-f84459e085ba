/* Variables CSS pour la cohérence */
:root {
  --primary-color: #1976d2;
  --primary-dark: #1565c0;
  --secondary-color: #f5f5f5;
  --success-color: #4caf50;
  --danger-color: #dc3545;
  --warning-color: #ff9800;
  --text-primary: #333;
  --text-secondary: #666;
  --border-color: #ddd;
  --shadow: 0 2px 8px rgba(0,0,0,0.1);
  --shadow-hover: 0 4px 16px rgba(0,0,0,0.15);
  --border-radius: 8px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* Reset et base */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  margin: 0;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  color: var(--text-primary);
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Cards améliorées */
.card {
  background: #fff;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin: var(--spacing-md) 0;
  transition: box-shadow 0.3s ease, transform 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-2px);
}

/* Balance card avec gradient */
.balance {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  text-align: center;
}

.balance > div {
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
}

.balance .big {
  font-weight: 700;
  font-size: 2rem;
  margin-top: var(--spacing-sm);
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Formulaires modernisés */
.form .row {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-md);
}

.form label {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.form input, .form select {
  padding: 12px var(--spacing-md);
  border-radius: var(--border-radius);
  border: 2px solid var(--border-color);
  font-size: 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form input:focus, .form select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25,118,210,0.1);
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

/* Boutons améliorés */
button {
  padding: 12px var(--spacing-lg);
  border-radius: var(--border-radius);
  border: none;
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Boutons de couleurs différentes */
button.danger {
  background: var(--danger-color);
}

button.danger:hover {
  background: #c82333;
}

button.success {
  background: var(--success-color);
}

button.success:hover {
  background: #45a049;
}

/* Tables responsives */
.table-container {
  overflow-x: auto;
  margin-top: var(--spacing-md);
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

th, td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

th {
  background: var(--secondary-color);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
}

tr:hover {
  background: #f9f9f9;
}

tr:last-child td {
  border-bottom: none;
}

/* Styles pour l'authentification améliorés */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: calc(-1 * var(--spacing-md));
  padding: var(--spacing-md);
}

.login-card {
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(20px);
  padding: var(--spacing-xl);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 420px;
  text-align: center;
  border: 1px solid rgba(255,255,255,0.2);
}

.login-card h1 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  font-size: 2.2rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.login-card h2 {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  font-weight: 400;
  font-size: 1.1rem;
}

.form-group {
  margin-bottom: var(--spacing-md);
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input {
  width: 100%;
  padding: 15px var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(255,255,255,0.9);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25,118,210,0.1);
  background: white;
}

.login-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(25,118,210,0.3);
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  color: #c62828;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  border: 1px solid #ffcdd2;
  font-weight: 500;
}

.login-info {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-md);
  background: rgba(245,245,245,0.8);
  border-radius: var(--border-radius);
  font-size: 14px;
  color: var(--text-secondary);
  border: 1px solid rgba(221,221,221,0.5);
}

.login-info p {
  margin: var(--spacing-xs) 0;
}

/* Header avec info utilisateur amélioré */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid rgba(255,255,255,0.2);
}

.app-header h1 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.8rem;
  font-weight: 700;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 14px;
  color: var(--text-secondary);
}

.user-info strong {
  color: var(--text-primary);
}

.logout-button {
  background: var(--danger-color);
  font-size: 12px;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
}

.logout-button:hover {
  background: #c82333;
}

/* États de chargement */
.loading {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.loading::after {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-left: var(--spacing-sm);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .nav-header {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .nav-menu {
    justify-content: center;
    flex-wrap: wrap;
  }

  .nav-item {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .nav-label {
    display: none;
  }

  .nav-icon {
    font-size: 1.5rem;
  }

  .balance {
    grid-template-columns: 1fr;
  }

  .form .row {
    margin-bottom: var(--spacing-sm);
  }

  .form-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .actions {
    flex-direction: column;
  }

  .actions button {
    width: 100%;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .recent-transaction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .transaction-amount {
    align-self: flex-end;
  }

  table {
    font-size: 14px;
  }

  th, td {
    padding: var(--spacing-sm);
  }

  .login-card {
    margin: var(--spacing-md);
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  body {
    padding: var(--spacing-sm);
  }

  .card {
    padding: var(--spacing-md);
  }

  .balance .big {
    font-size: 1.5rem;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }
}

/* Animations et transitions */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.card {
  animation: fadeIn 0.5s ease-out;
}

/* Améliorations pour l'accessibilité */
button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

input:focus, select:focus {
  outline: none;
}

/* Styles pour les messages de succès */
.success-message {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  color: #2e7d32;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  border: 1px solid #c8e6c9;
  font-weight: 500;
}

/* Styles pour les badges/chips */
.badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--secondary-color);
  color: var(--text-secondary);
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.badge.income {
  background: #e8f5e8;
  color: #2e7d32;
}

.badge.expense {
  background: #ffebee;
  color: #c62828;
}

/* Navigation */
.navigation {
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--spacing-lg);
  border: 1px solid rgba(255,255,255,0.2);
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.nav-header h1 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.8rem;
  font-weight: 700;
}

.nav-menu {
  display: flex;
  padding: var(--spacing-md);
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  background: transparent;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.nav-item:hover {
  background: rgba(25,118,210,0.1);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow);
}

.nav-item.active:hover {
  background: var(--primary-dark);
  color: white;
}

.nav-icon {
  font-size: 1.2rem;
}

.nav-label {
  font-size: 0.9rem;
}

/* Dashboard */
.dashboard-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.dashboard-header h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  font-size: 2rem;
}

.dashboard-header p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* Transactions récentes */
.recent-transactions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.recent-transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: #f8f9fa;
  border-radius: var(--border-radius);
  transition: background 0.2s ease;
}

.recent-transaction-item:hover {
  background: #e9ecef;
}

.transaction-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.transaction-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.transaction-description {
  font-weight: 600;
  color: var(--text-primary);
}

.transaction-category {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.transaction-amount {
  font-weight: 700;
  font-size: 1.1rem;
}

.transaction-amount.positive {
  color: var(--success-color);
}

.transaction-amount.negative {
  color: var(--danger-color);
}

/* État vide */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-state-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-state-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

/* Gestion des catégories */
.category-manager {
  max-width: 800px;
  margin: 0 auto;
}

.category-form {
  margin-top: var(--spacing-md);
}

.form-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-end;
}

.form-row input {
  flex: 1;
  padding: 12px var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 16px;
}

.form-row button {
  white-space: nowrap;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: #f8f9fa;
  border-radius: var(--border-radius);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.category-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.category-name {
  font-weight: 600;
  color: var(--text-primary);
}

.delete-category-btn {
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.delete-category-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

.tips-card {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  border: 1px solid #ffcc02;
}

.tips-list {
  margin: var(--spacing-md) 0 0 0;
  padding-left: var(--spacing-lg);
}

.tips-list li {
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.init-card {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border: 2px solid #2196f3;
  text-align: center;
}

.init-card h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.init-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}