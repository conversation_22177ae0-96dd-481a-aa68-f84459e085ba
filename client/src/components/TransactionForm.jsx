import React, { useState } from 'react'

export default function TransactionForm({ categories, onSaved, apiBase, authToken }){
  const [amount, setAmount] = useState('')
  const [type, setType] = useState('out')
  const [category, setCategory] = useState('')
  const [description, setDescription] = useState('')

  const submit = async (e) => {
    e.preventDefault()
    const a = parseFloat(amount)
    if (Number.isNaN(a)) return alert('Montant invalide')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(apiBase + '/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ amount: Math.abs(a), type, category_id: category || null, description })
      })

      if (response.ok) {
        setAmount(''); setDescription('')
        onSaved()
      } else {
        alert('Erreur lors de l\'enregistrement')
      }
    } catch (error) {
      alert('Erreur de connexion')
    }
  }

  return (
    <form className="card form" onSubmit={submit}>
      <h3>Ajouter une transaction</h3>
      <div className="row">
        <label>Montant</label>
        <input value={amount} onChange={e=>setAmount(e.target.value)} placeholder="eg. 12.50" />
      </div>
      <div className="row">
        <label>Type</label>
        <select value={type} onChange={e=>setType(e.target.value)}>
          <option value="in">Entrée</option>
          <option value="out">Dépense</option>
        </select>
      </div>
      <div className="row">
        <label>Catégorie</label>
        <select value={category} onChange={e=>setCategory(e.target.value)}>
          <option value="">— aucune —</option>
          {categories.map(c=> <option key={c.id} value={c.id}>{c.name}</option>)}
        </select>
      </div>
      <div className="row">
        <label>Note</label>
        <input value={description} onChange={e=>setDescription(e.target.value)} placeholder="facultatif" />
      </div>
      <div className="actions">
        <button type="submit">Enregistrer</button>
      </div>
    </form>
  )
}
