const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const db = require('./db');

const app = express();
app.use(cors({
  origin: 'http://localhost:5173', // URL du client React
  credentials: true
}));
app.use(bodyParser.json());

// Stockage temporaire des sessions en mémoire (à remplacer par une vraie solution en production)
const sessions = new Map();

// Middleware d'authentification
const requireAuth = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token || !sessions.has(token)) {
    return res.status(401).json({ error: 'Non authentifié' });
  }
  req.user = sessions.get(token);
  next();
};

// Helper statements
const getUserByUsername = db.prepare('SELECT id, username, password_hash FROM users WHERE username = ?');
const insertCategory = db.prepare('INSERT OR IGNORE INTO categories (name, user_id) VALUES (?, ?)');
const getCategories = db.prepare('SELECT id, name FROM categories WHERE user_id = ? ORDER BY name');
const insertTransaction = db.prepare('INSERT INTO transactions (amount, type, category_id, description, date_iso, user_id) VALUES (?, ?, ?, ?, ?, ?)');
const getTransactions = db.prepare('SELECT t.id, t.amount, t.type, t.description, t.date_iso, c.id as category_id, c.name as category_name FROM transactions t LEFT JOIN categories c ON t.category_id = c.id WHERE t.user_id = ? ORDER BY date_iso DESC');
const deleteTransaction = db.prepare('DELETE FROM transactions WHERE id = ? AND user_id = ?');
const getBalance = db.prepare(`SELECT
  COALESCE(SUM(CASE WHEN type='in' THEN amount ELSE 0 END),0) as total_in,
  COALESCE(SUM(CASE WHEN type='out' THEN amount ELSE 0 END),0) as total_out
FROM transactions WHERE user_id = ?`);

// Routes d'authentification
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;
  if (!username || !password) {
    return res.status(400).json({ error: 'Nom d\'utilisateur et mot de passe requis' });
  }

  const user = getUserByUsername.get(username);
  if (!user) {
    return res.status(401).json({ error: 'Identifiants invalides' });
  }

  // Vérification simple du mot de passe (à améliorer avec bcrypt)
  if (password !== 'password') { // Mot de passe temporaire
    return res.status(401).json({ error: 'Identifiants invalides' });
  }

  // Créer un token simple (à remplacer par JWT en production)
  const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
  sessions.set(token, { id: user.id, username: user.username });

  res.json({ success: true, username: user.username, token });
});

app.post('/api/logout', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token) {
    sessions.delete(token);
  }
  res.json({ success: true });
});

app.get('/api/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token || !sessions.has(token)) {
    return res.status(401).json({ error: 'Non authentifié' });
  }
  const user = sessions.get(token);
  res.json({
    authenticated: true,
    username: user.username,
    userId: user.id
  });
});

// Routes protégées
app.get('/api/categories', requireAuth, (req, res) => {
  const rows = getCategories.all(req.user.id);
  res.json(rows);
});

app.post('/api/categories', requireAuth, (req, res) => {
  const { name } = req.body;
  if (!name || !name.trim()) return res.status(400).json({ error: 'Nom requis' });
  insertCategory.run(name.trim(), req.user.id);
  res.status(201).json({ ok: true });
});

app.get('/api/transactions', requireAuth, (req, res) => {
  const rows = getTransactions.all(req.user.id);
  res.json(rows);
});

app.post('/api/transactions', requireAuth, (req, res) => {
  const { amount, type, category_id, description, date_iso } = req.body;
  if (typeof amount !== 'number' || !['in','out'].includes(type)) return res.status(400).json({ error: 'Données invalides' });
  const dateISO = date_iso || new Date().toISOString();
  const catId = category_id || null;
  const info = insertTransaction.run(amount, type, catId, description || null, dateISO, req.user.id);
  res.status(201).json({ id: info.lastInsertRowid });
});

app.delete('/api/transactions/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  deleteTransaction.run(id, req.user.id);
  res.json({ ok: true });
});

app.get('/api/balance', requireAuth, (req, res) => {
  const row = getBalance.get(req.user.id);
  row.balance = row.total_in - row.total_out;
  res.json(row);
});

const PORT = process.env.PORT || 3333;
app.listen(PORT, () => console.log(`Server started on http://localhost:${PORT}`));