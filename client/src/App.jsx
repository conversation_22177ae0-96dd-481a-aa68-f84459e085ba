import React, { useEffect, useState } from 'react'
import TransactionForm from './components/TransactionForm'
import TransactionList from './components/TransactionList'
import BalanceCard from './components/BalanceCard'

const API = import.meta.env.VITE_API_BASE || 'http://localhost:3333/api'

export default function App(){
  const [transactions, setTransactions] = useState([])
  const [categories, setCategories] = useState([])
  const [balance, setBalance] = useState({ total_in:0, total_out:0, balance:0 })

  const load = async () => {
    const [tRes, cRes, bRes] = await Promise.all([
      fetch(API + '/transactions'),
      fetch(API + '/categories'),
      fetch(API + '/balance')
    ])
    setTransactions(await tRes.json())
    setCategories(await cRes.json())
    setBalance(await bRes.json())
  }

  useEffect(()=>{ load() }, [])

  return (
    <div className="container">
      <h1>Simple Perso Compta</h1>
      <BalanceCard balance={balance} />
      <TransactionForm categories={categories} onSaved={load} apiBase={API} />
      <TransactionList transactions={transactions} onDeleted={load} />
    </div>
  )
}