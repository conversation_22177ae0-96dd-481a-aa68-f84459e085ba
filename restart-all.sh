#!/bin/bash

echo "🔄 Redémarrage complet de l'application Binou Compta Perso..."
echo "=================================================="

# Arrêter tous les processus
echo "🛑 Arrêt des processus en cours..."
pkill -f "node index.js" 2>/dev/null || echo "Aucun serveur backend à arrêter"
pkill -f "vite" 2>/dev/null || echo "Aucun client React à arrêter"

# Attendre que les processus se terminent
echo "⏳ Attente de l'arrêt des processus..."
sleep 3

echo ""
echo "🚀 Démarrage du serveur backend..."
echo "=================================="
cd server
npm start > ../server.log 2>&1 &
SERVER_PID=$!
cd ..

# Attendre que le serveur démarre
sleep 4

echo ""
echo "🚀 Démarrage du client React..."
echo "==============================="
cd client
npm run dev > ../client.log 2>&1 &
CLIENT_PID=$!
cd ..

# Attendre que le client démarre
sleep 6

echo ""
echo "✅ Application redémarrée avec succès !"
echo "======================================="
echo "🌐 Application web : http://localhost:5173"
echo "🔧 API Backend    : http://localhost:3333"
echo ""
echo "📊 Logs serveur   : tail -f server.log"
echo "📊 Logs client    : tail -f client.log"
echo ""
echo "🛑 Pour tout arrêter : ./stop-all.sh"
echo ""
echo "PIDs des processus :"
echo "- Serveur : $SERVER_PID"
echo "- Client  : $CLIENT_PID"

# Sauvegarder les PIDs pour pouvoir les arrêter plus tard
echo $SERVER_PID > server.pid
echo $CLIENT_PID > client.pid
