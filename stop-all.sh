#!/bin/bash

echo "🛑 Arrêt de l'application Binou Compta Perso..."
echo "=============================================="

# Arrêter via les PIDs sauvegardés si disponibles
if [ -f "server.pid" ]; then
    SERVER_PID=$(cat server.pid)
    echo "🔴 Arrêt du serveur (PID: $SERVER_PID)..."
    kill $SERVER_PID 2>/dev/null || echo "Serveur déjà arrêté"
    rm server.pid
fi

if [ -f "client.pid" ]; then
    CLIENT_PID=$(cat client.pid)
    echo "🔴 Arrêt du client (PID: $CLIENT_PID)..."
    kill $CLIENT_PID 2>/dev/null || echo "Client déjà arrêté"
    rm client.pid
fi

# Arrêter tous les processus par nom (au cas où)
echo "🧹 Nettoyage des processus restants..."
pkill -f "node index.js" 2>/dev/null || echo "Aucun serveur Node.js à arrêter"
pkill -f "vite" 2>/dev/null || echo "Aucun client Vite à arrêter"

# Attendre que les processus se terminent
sleep 2

echo ""
echo "✅ Tous les processus ont été arrêtés !"
echo "🗂️  Les fichiers de logs sont conservés :"
echo "   - server.log"
echo "   - client.log"
