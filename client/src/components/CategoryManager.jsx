import React, { useState } from 'react'

export default function CategoryManager({ categories, onCategoryAdded, onCategoryDeleted, apiBase, authToken }) {
  const [newCategoryName, setNewCategoryName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleAddCategory = async (e) => {
    e.preventDefault()
    if (!newCategoryName.trim()) return

    setLoading(true)
    setError('')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/categories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ name: newCategoryName.trim() })
      })

      const data = await response.json()

      if (response.ok) {
        setNewCategoryName('')
        onCategoryAdded()
      } else {
        setError(data.error || 'Erreur lors de l\'ajout')
      }
    } catch (error) {
      setError('Erreur de connexion')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteCategory = async (categoryId, categoryName) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${categoryName}" ?\n\nAttention : Cette action est irréversible.`)) {
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/categories/${categoryId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        onCategoryDeleted()
      } else {
        const data = await response.json()
        alert(data.error || 'Erreur lors de la suppression')
      }
    } catch (error) {
      alert('Erreur de connexion')
    }
  }

  return (
    <div className="category-manager">
      <div className="dashboard-header">
        <h2>🏷️ Gestion des catégories</h2>
        <p>Organisez vos transactions avec des catégories personnalisées</p>
      </div>

      {/* Formulaire d'ajout */}
      <div className="card">
        <h3>➕ Ajouter une nouvelle catégorie</h3>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <form onSubmit={handleAddCategory} className="category-form">
          <div className="form-row">
            <input
              type="text"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Nom de la catégorie (ex: 🍕 Restaurant)"
              disabled={loading}
              maxLength="50"
            />
            <button 
              type="submit" 
              disabled={loading || !newCategoryName.trim()}
              className="success"
            >
              {loading ? '⏳ Ajout...' : '✅ Ajouter'}
            </button>
          </div>
        </form>
      </div>

      {/* Liste des catégories */}
      <div className="card">
        <h3>📋 Vos catégories ({categories.length})</h3>
        
        {categories.length === 0 ? (
          <div className="empty-state">
            <div className="empty-state-icon">🏷️</div>
            <p>Aucune catégorie pour le moment</p>
            <p style={{ fontSize: '0.9rem', color: '#666' }}>
              Ajoutez votre première catégorie ci-dessus
            </p>
          </div>
        ) : (
          <div className="categories-grid">
            {categories.map(category => (
              <div key={category.id} className="category-item">
                <div className="category-info">
                  <span className="category-name">{category.name}</span>
                </div>
                <button
                  onClick={() => handleDeleteCategory(category.id, category.name)}
                  className="delete-category-btn"
                  title="Supprimer cette catégorie"
                >
                  🗑️
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Conseils */}
      <div className="card tips-card">
        <h3>💡 Conseils</h3>
        <ul className="tips-list">
          <li>Utilisez des émojis pour rendre vos catégories plus visuelles</li>
          <li>Créez des catégories spécifiques pour un meilleur suivi</li>
          <li>Les catégories supprimées ne peuvent pas être récupérées</li>
          <li>Vous pouvez avoir jusqu'à 50 caractères par nom de catégorie</li>
        </ul>
      </div>
    </div>
  )
}
