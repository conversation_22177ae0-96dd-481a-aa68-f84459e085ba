const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

const DB_DIR = path.join(__dirname, 'data');
if (!fs.existsSync(DB_DIR)) fs.mkdirSync(DB_DIR, { recursive: true });
const DB_PATH = path.join(DB_DIR, 'spc.db');

const db = new sqlite3.Database(DB_PATH);

// Enable WAL mode for better performance
db.run('PRAGMA journal_mode = WAL');

// Create tables if not exists
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT NOT NULL UNIQUE,
      password_hash TEXT NOT NULL,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  db.run(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      user_id INTEGER NOT NULL,
      FOREIGN KEY(user_id) REFERENCES users(id),
      UNIQUE(name, user_id)
    )
  `);

  db.run(`
    CREATE TABLE IF NOT EXISTS transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      amount REAL NOT NULL,
      type TEXT NOT NULL CHECK(type IN ('in','out')),
      category_id INTEGER,
      description TEXT,
      date_iso TEXT NOT NULL,
      user_id INTEGER NOT NULL,
      FOREIGN KEY(category_id) REFERENCES categories(id),
      FOREIGN KEY(user_id) REFERENCES users(id)
    )
  `);

  // Créer un utilisateur par défaut si aucun n'existe
  db.get('SELECT COUNT(*) as count FROM users', (err, row) => {
    if (err) {
      console.error('Erreur lors de la vérification des utilisateurs:', err);
      return;
    }

    if (row.count === 0) {
      // Mot de passe par défaut : "password" (à changer en production)
      const defaultPasswordHash = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'; // bcrypt hash pour "password"
      db.run('INSERT INTO users (username, password_hash) VALUES (?, ?)', ['admin', defaultPasswordHash], function(err) {
        if (err) {
          console.error('Erreur lors de la création de l\'utilisateur par défaut:', err);
        } else {
          console.log('Utilisateur par défaut créé: admin / password');
          const userId = this.lastID;

          // Créer les catégories par défaut pour l'utilisateur
          const defaultCategories = [
            '🏠 Logement',
            '🚗 Transport',
            '🍽️ Alimentation',
            '📱 Téléphone',
            '💡 Électricité',
            '💧 Eau',
            '🌐 Internet',
            '🏥 Santé',
            '👕 Vêtements',
            '🎬 Loisirs',
            '📚 Éducation',
            '🎁 Cadeaux',
            '💰 Épargne',
            '🏪 Courses',
            '⛽ Carburant',
            '🔧 Réparations',
            '📄 Assurances',
            '🏦 Banque',
            '💼 Travail',
            '🎯 Autres'
          ];

          defaultCategories.forEach(categoryName => {
            db.run('INSERT INTO categories (name, user_id) VALUES (?, ?)', [categoryName, userId], (err) => {
              if (err) {
                console.error('Erreur lors de la création de la catégorie:', categoryName, err);
              }
            });
          });

          console.log('Catégories par défaut créées');
        }
      });
    }
  });
});

module.exports = db;
