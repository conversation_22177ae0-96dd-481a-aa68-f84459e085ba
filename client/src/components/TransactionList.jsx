import React from 'react'

export default function TransactionList({ transactions, onDeleted, apiBase, authToken }){
  const del = async (id) => {
    if (!confirm('Supprimer cette transaction ?')) return
    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/transactions/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (response.ok) {
        onDeleted()
      } else {
        alert('Erreur lors de la suppression')
      }
    } catch (error) {
      alert('Erreur de connexion')
    }
  }
  return (
    <div className="card list">
      <h3>Transactions récentes</h3>
      <table>
        <thead>
          <tr><th>Date</th><th>Montant</th><th>Type</th><th>Catégorie</th><th>Note</th><th></th></tr>
        </thead>
        <tbody>
          {transactions.map(t => (
            <tr key={t.id}>
              <td>{new Date(t.date_iso).toLocaleString()}</td>
              <td>{t.amount.toFixed(2)} €</td>
              <td>{t.type === 'in' ? 'Entrée' : 'Dépense'}</td>
              <td>{t.category_name || '-'}</td>
              <td>{t.description || ''}</td>
              <td><button onClick={()=>del(t.id)}>Suppr</button></td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}