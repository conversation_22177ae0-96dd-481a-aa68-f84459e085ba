import React, { useState } from 'react'

export default function CategoryManager({ categories, onCategoryAdded, onCategoryDeleted, apiBase, authToken }) {
  const [newCategoryName, setNewCategoryName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [initLoading, setInitLoading] = useState(false)

  const handleAddCategory = async (e) => {
    e.preventDefault()
    if (!newCategoryName.trim()) return

    setLoading(true)
    setError('')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/categories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ name: newCategoryName.trim() })
      })

      const data = await response.json()

      if (response.ok) {
        setNewCategoryName('')
        onCategoryAdded()
      } else {
        setError(data.error || 'Erreur lors de l\'ajout')
      }
    } catch (error) {
      setError('Erreur de connexion')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteCategory = async (categoryId, categoryName) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${categoryName}" ?\n\nAttention : Cette action est irréversible.`)) {
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/categories/${categoryId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        onCategoryDeleted()
      } else {
        const data = await response.json()
        alert(data.error || 'Erreur lors de la suppression')
      }
    } catch (error) {
      alert('Erreur de connexion')
    }
  }

  const handleInitDefaults = async () => {
    if (!confirm('Voulez-vous ajouter les catégories par défaut ?\n\nCela ajoutera 20 catégories courantes comme Logement, Transport, Alimentation, etc.')) {
      return
    }

    setInitLoading(true)
    setError('')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/categories/init-defaults`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()

      if (response.ok) {
        onCategoryAdded()
        alert(`✅ ${data.created} catégories ajoutées avec succès !`)
      } else {
        setError(data.error || 'Erreur lors de l\'initialisation')
      }
    } catch (error) {
      setError('Erreur de connexion')
    } finally {
      setInitLoading(false)
    }
  }

  return (
    <div className="category-manager">
      <div className="dashboard-header">
        <h2>🏷️ Gestion des catégories</h2>
        <p>Organisez vos transactions avec des catégories personnalisées</p>
      </div>

      {/* Formulaire d'ajout */}
      <div className="card">
        <h3>➕ Ajouter une nouvelle catégorie</h3>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <form onSubmit={handleAddCategory} className="category-form">
          <div className="form-row">
            <input
              type="text"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Nom de la catégorie (ex: 🍕 Restaurant)"
              disabled={loading}
              maxLength="50"
            />
            <button 
              type="submit" 
              disabled={loading || !newCategoryName.trim()}
              className="success"
            >
              {loading ? '⏳ Ajout...' : '✅ Ajouter'}
            </button>
          </div>
        </form>
      </div>

      {/* Initialisation des catégories par défaut */}
      {categories.length === 0 && (
        <div className="card init-card">
          <h3>🚀 Démarrage rapide</h3>
          <p>Vous n'avez encore aucune catégorie. Voulez-vous ajouter les catégories les plus courantes ?</p>
          <div style={{ textAlign: 'center', marginTop: '16px' }}>
            <button
              onClick={handleInitDefaults}
              disabled={initLoading}
              className="success"
              style={{ fontSize: '16px', padding: '12px 24px' }}
            >
              {initLoading ? '⏳ Ajout en cours...' : '✨ Ajouter les catégories par défaut'}
            </button>
          </div>
          <div style={{ fontSize: '0.9rem', color: '#666', marginTop: '12px', textAlign: 'center' }}>
            Cela ajoutera 20 catégories comme : 🏠 Logement, 🚗 Transport, 🍽️ Alimentation, etc.
          </div>
        </div>
      )}

      {/* Liste des catégories */}
      <div className="card">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3>📋 Vos catégories ({categories.length})</h3>
          {categories.length > 0 && (
            <button
              onClick={handleInitDefaults}
              disabled={initLoading}
              className="success"
              style={{ fontSize: '12px', padding: '6px 12px' }}
            >
              {initLoading ? '⏳ Ajout...' : '➕ Ajouter catégories par défaut'}
            </button>
          )}
        </div>
        
        {categories.length === 0 ? (
          <div className="empty-state">
            <div className="empty-state-icon">🏷️</div>
            <p>Aucune catégorie pour le moment</p>
            <p style={{ fontSize: '0.9rem', color: '#666' }}>
              Ajoutez votre première catégorie ci-dessus
            </p>
          </div>
        ) : (
          <div className="categories-grid">
            {categories.map(category => (
              <div key={category.id} className="category-item">
                <div className="category-info">
                  <span className="category-name">{category.name}</span>
                </div>
                <button
                  onClick={() => handleDeleteCategory(category.id, category.name)}
                  className="delete-category-btn"
                  title="Supprimer cette catégorie"
                >
                  🗑️
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Conseils */}
      <div className="card tips-card">
        <h3>💡 Conseils</h3>
        <ul className="tips-list">
          <li>Utilisez des émojis pour rendre vos catégories plus visuelles</li>
          <li>Créez des catégories spécifiques pour un meilleur suivi</li>
          <li>Les catégories supprimées ne peuvent pas être récupérées</li>
          <li>Vous pouvez avoir jusqu'à 50 caractères par nom de catégorie</li>
        </ul>
      </div>
    </div>
  )
}
