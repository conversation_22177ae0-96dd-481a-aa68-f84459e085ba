import React, { useEffect, useState } from 'react'
import TransactionForm from './components/TransactionForm'
import TransactionList from './components/TransactionList'
import Dashboard from './components/Dashboard'
import CategoryManager from './components/CategoryManager'
import Navigation from './components/Navigation'
import LoginForm from './components/LoginForm'
import Toast from './components/Toast'

const API = import.meta.env.VITE_API_BASE || 'http://localhost:3333/api'

export default function App(){
  const [transactions, setTransactions] = useState([])
  const [categories, setCategories] = useState([])
  const [balance, setBalance] = useState({ total_in:0, total_out:0, balance:0 })
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [authToken, setAuthToken] = useState(null)
  const [toast, setToast] = useState(null)
  const [currentView, setCurrentView] = useState('dashboard')

  const getAuthHeaders = () => {
    const token = authToken || localStorage.getItem('authToken')
    return token ? { 'Authorization': `Bearer ${token}` } : {}
  }

  const load = async () => {
    try {
      const headers = getAuthHeaders()
      const [tRes, cRes, bRes] = await Promise.all([
        fetch(API + '/transactions', { headers }),
        fetch(API + '/categories', { headers }),
        fetch(API + '/balance', { headers })
      ])

      if (tRes.ok && cRes.ok && bRes.ok) {
        setTransactions(await tRes.json())
        setCategories(await cRes.json())
        setBalance(await bRes.json())
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error)
    }
  }

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('authToken')
      if (!token) {
        setLoading(false)
        return
      }

      const response = await fetch(API + '/me', {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        const userData = await response.json()
        setUser(userData.username)
        setAuthToken(token)
        await load()
      } else {
        // Token invalide, le supprimer
        localStorage.removeItem('authToken')
      }
    } catch (error) {
      console.error('Erreur lors de la vérification de l\'authentification:', error)
    } finally {
      setLoading(false)
    }
  }

  const showToast = (message, type = 'info') => {
    setToast({ message, type })
  }

  const hideToast = () => {
    setToast(null)
  }

  const handleLogin = (username, token) => {
    setUser(username)
    setAuthToken(token)
    setCurrentView('dashboard')
    showToast(`Bienvenue ${username} !`, 'success')
    load()
  }

  const handleLogout = async () => {
    try {
      const token = authToken || localStorage.getItem('authToken')
      if (token) {
        await fetch(API + '/logout', {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${token}` }
        })
      }
      localStorage.removeItem('authToken')
      setUser(null)
      setAuthToken(null)
      setTransactions([])
      setCategories([])
      setBalance({ total_in:0, total_out:0, balance:0 })
      showToast('Déconnexion réussie', 'info')
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error)
      showToast('Erreur lors de la déconnexion', 'error')
    }
  }

  useEffect(() => {
    checkAuth()
  }, [])

  if (loading) {
    return (
      <div className="container">
        <div className="loading">Chargement...</div>
      </div>
    )
  }

  if (!user) {
    return <LoginForm onLogin={handleLogin} apiBase={API} />
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard balance={balance} transactions={transactions} />

      case 'add-transaction':
        return (
          <TransactionForm
            categories={categories}
            onSaved={() => {
              load()
              showToast('Transaction ajoutée avec succès !', 'success')
              setCurrentView('dashboard') // Retour au dashboard après ajout
            }}
            apiBase={API}
            authToken={authToken}
          />
        )

      case 'categories':
        return (
          <CategoryManager
            categories={categories}
            onCategoryAdded={() => {
              load()
              showToast('Catégorie ajoutée avec succès !', 'success')
            }}
            onCategoryDeleted={() => {
              load()
              showToast('Catégorie supprimée', 'info')
            }}
            apiBase={API}
            authToken={authToken}
          />
        )

      case 'transactions':
        return (
          <TransactionList
            transactions={transactions}
            onDeleted={() => {
              load()
              showToast('Transaction supprimée', 'info')
            }}
            apiBase={API}
            authToken={authToken}
          />
        )

      default:
        return <Dashboard balance={balance} transactions={transactions} />
    }
  }

  return (
    <div className="container">
      <Navigation
        currentView={currentView}
        onViewChange={setCurrentView}
        user={user}
        onLogout={handleLogout}
      />

      <main>
        {renderCurrentView()}
      </main>

      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={hideToast}
        />
      )}
    </div>
  )
}