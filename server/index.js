const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const db = require('./db');

const app = express();
app.use(cors());
app.use(bodyParser.json());

// Helper statements
const insertCategory = db.prepare('INSERT OR IGNORE INTO categories (name) VALUES (?)');
const getCategories = db.prepare('SELECT id, name FROM categories ORDER BY name');
const insertTransaction = db.prepare('INSERT INTO transactions (amount, type, category_id, description, date_iso) VALUES (?, ?, ?, ?, ?)');
const getTransactions = db.prepare('SELECT t.id, t.amount, t.type, t.description, t.date_iso, c.id as category_id, c.name as category_name FROM transactions t LEFT JOIN categories c ON t.category_id = c.id ORDER BY date_iso DESC');
const deleteTransaction = db.prepare('DELETE FROM transactions WHERE id = ?');

app.get('/api/categories', (req, res) => {
  const rows = getCategories.all();
  res.json(rows);
});

app.post('/api/categories', (req, res) => {
  const { name } = req.body;
  if (!name || !name.trim()) return res.status(400).json({ error: 'Nom requis' });
  insertCategory.run(name.trim());
  res.status(201).json({ ok: true });
});

app.get('/api/transactions', (req, res) => {
  const rows = getTransactions.all();
  res.json(rows);
});

app.post('/api/transactions', (req, res) => {
  const { amount, type, category_id, description, date_iso } = req.body;
  if (typeof amount !== 'number' || !['in','out'].includes(type)) return res.status(400).json({ error: 'Données invalides' });
  const dateISO = date_iso || new Date().toISOString();
  const catId = category_id || null;
  const info = insertTransaction.run(amount, type, catId, description || null, dateISO);
  res.status(201).json({ id: info.lastInsertRowid });
});

app.delete('/api/transactions/:id', (req, res) => {
  const id = Number(req.params.id);
  deleteTransaction.run(id);
  res.json({ ok: true });
});

app.get('/api/balance', (req, res) => {
  const row = db.prepare(`SELECT
    COALESCE(SUM(CASE WHEN type='in' THEN amount ELSE 0 END),0) as total_in,
    COALESCE(SUM(CASE WHEN type='out' THEN amount ELSE 0 END),0) as total_out
  FROM transactions`).get();
  row.balance = row.total_in - row.total_out;
  res.json(row);
});

const PORT = process.env.PORT || 3333;
app.listen(PORT, () => console.log(`Server started on http://localhost:${PORT}`));