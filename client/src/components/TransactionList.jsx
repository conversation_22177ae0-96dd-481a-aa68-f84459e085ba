import React from 'react'

export default function TransactionList({ transactions, onDeleted }){
  const del = async (id) => {
    if (!confirm('Supprimer cette transaction ?')) return
    await fetch(`http://localhost:3333/api/transactions/${id}`, { method:'DELETE' })
    onDeleted()
  }
  return (
    <div className="card list">
      <h3>Transactions récentes</h3>
      <table>
        <thead>
          <tr><th>Date</th><th>Montant</th><th>Type</th><th>Catégorie</th><th>Note</th><th></th></tr>
        </thead>
        <tbody>
          {transactions.map(t => (
            <tr key={t.id}>
              <td>{new Date(t.date_iso).toLocaleString()}</td>
              <td>{t.amount.toFixed(2)} €</td>
              <td>{t.type === 'in' ? 'Entrée' : 'Dépense'}</td>
              <td>{t.category_name || '-'}</td>
              <td>{t.description || ''}</td>
              <td><button onClick={()=>del(t.id)}>Suppr</button></td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}