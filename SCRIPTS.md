# 🚀 Scripts de gestion - Binou Compta <PERSON>so

## 📋 Scripts disponibles

### 🔄 **Redémarrage rapide**
```bash
# Redémarrer tout (serveur + client)
./restart-all.sh

# Redémarrer seulement le serveur
./restart-server.sh

# Redémarrer seulement le client
./restart-client.sh
```

### 🛑 **Arrêt**
```bash
# Arrêter tous les processus
./stop-all.sh
```

### 🚀 **Mode développement** (Recommandé)
```bash
# Démarrer avec rechargement automatique
./dev.sh
```

## 📊 **Surveillance des logs**

### Logs en temps réel
```bash
# Logs du serveur
tail -f server.log

# Logs du client  
tail -f client.log

# Logs mode développement
tail -f server-dev.log
tail -f client-dev.log
```

### Logs des dernières erreurs
```bash
# Dernières erreurs serveur
tail -20 server.log | grep -i error

# Dernières erreurs client
tail -20 client.log | grep -i error
```

## 🔧 **Commandes manuelles**

### Démarrage manuel
```bash
# Serveur backend
cd server && npm start

# Client React
cd client && npm run dev
```

### Arrêt manuel
```bash
# Arrêter tous les processus Node.js
pkill -f "node index.js"

# Arrêter tous les processus Vite
pkill -f "vite"
```

## 🌐 **URLs de l'application**

- **Application web** : http://localhost:5173
- **API Backend** : http://localhost:3333
- **Login par défaut** : `admin` / `password`

## 💡 **Conseils d'utilisation**

1. **Pour le développement** : Utilisez `./dev.sh` - il redémarre automatiquement quand vous modifiez le code
2. **Pour les tests** : Utilisez `./restart-all.sh` après des modifications importantes
3. **Pour arrêter proprement** : Toujours utiliser `./stop-all.sh` plutôt que Ctrl+C
4. **Surveillance** : Gardez un terminal ouvert avec `tail -f server.log` pour voir les erreurs

## 🐛 **Dépannage**

### Si les ports sont occupés
```bash
# Voir qui utilise le port 3333 (serveur)
lsof -i :3333

# Voir qui utilise le port 5173 (client)
lsof -i :5173

# Forcer l'arrêt d'un processus
kill -9 <PID>
```

### Si les scripts ne fonctionnent pas
```bash
# Rendre les scripts exécutables
chmod +x *.sh

# Vérifier les permissions
ls -la *.sh
```
