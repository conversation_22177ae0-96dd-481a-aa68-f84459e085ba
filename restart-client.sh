#!/bin/bash

echo "🔄 Redémarrage du client React..."

# Arrêter le client s'il est en cours d'exécution
echo "📋 Recherche des processus Vite en cours..."
pkill -f "vite" 2>/dev/null || echo "Aucun client à arrêter"

# Attendre un peu pour que le processus se termine
sleep 2

# Aller dans le dossier client
cd client

echo "🚀 Démarrage du client React..."
# Démarrer le client en arrière-plan
npm run dev &

# Attendre que le client démarre
sleep 5

echo "✅ Client React redémarré !"
echo "🌐 Application disponible sur : http://localhost:5173"
echo ""
echo "📊 Pour voir les logs du client : tail -f client.log"
echo "🛑 Pour arrêter le client : pkill -f 'vite'"
