import React, { useState } from 'react'

export default function TransactionForm({ categories, onSaved, apiBase, authToken }){
  const [amount, setAmount] = useState('')
  const [type, setType] = useState('out')
  const [category, setCategory] = useState('')
  const [description, setDescription] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const submit = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    const a = parseFloat(amount)
    if (Number.isNaN(a) || a <= 0) {
      setError('Veuillez saisir un montant valide supérieur à 0')
      setLoading(false)
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(apiBase + '/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bear<PERSON> ${token}`
        },
        body: JSON.stringify({
          amount: Math.abs(a),
          type,
          category_id: category || null,
          description: description.trim() || null
        })
      })

      if (response.ok) {
        setAmount('')
        setDescription('')
        setCategory('')
        onSaved()
        // Message de succès temporaire
        setError('')
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de l\'enregistrement')
      }
    } catch (error) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form className="card form" onSubmit={submit}>
      <h3>➕ Ajouter une transaction</h3>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="row">
        <label htmlFor="amount">💰 Montant *</label>
        <input
          id="amount"
          type="number"
          step="0.01"
          min="0"
          value={amount}
          onChange={e => setAmount(e.target.value)}
          placeholder="ex: 12.50"
          required
          disabled={loading}
        />
      </div>

      <div className="row">
        <label htmlFor="type">📊 Type *</label>
        <select
          id="type"
          value={type}
          onChange={e => setType(e.target.value)}
          disabled={loading}
        >
          <option value="in">💰 Entrée d'argent</option>
          <option value="out">💸 Dépense</option>
        </select>
      </div>

      <div className="row">
        <label htmlFor="category">🏷️ Catégorie</label>
        <select
          id="category"
          value={category}
          onChange={e => setCategory(e.target.value)}
          disabled={loading}
        >
          <option value="">— Aucune catégorie —</option>
          {categories.map(c => (
            <option key={c.id} value={c.id}>{c.name}</option>
          ))}
        </select>
      </div>

      <div className="row">
        <label htmlFor="description">📝 Note (optionnel)</label>
        <input
          id="description"
          type="text"
          value={description}
          onChange={e => setDescription(e.target.value)}
          placeholder="Description de la transaction..."
          maxLength="255"
          disabled={loading}
        />
      </div>

      <div className="actions">
        <button
          type="submit"
          disabled={loading || !amount}
          className="success"
        >
          {loading ? '⏳ Enregistrement...' : '✅ Enregistrer'}
        </button>
      </div>
    </form>
  )
}
