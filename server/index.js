const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const db = require('./db');

const app = express();
app.use(cors({
  origin: 'http://localhost:5173', // URL du client React
  credentials: true
}));
app.use(bodyParser.json());

// Stockage temporaire des sessions en mémoire (à remplacer par une vraie solution en production)
const sessions = new Map();

// Middleware d'authentification
const requireAuth = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token || !sessions.has(token)) {
    return res.status(401).json({ error: 'Non authentifié' });
  }
  req.user = sessions.get(token);
  next();
};

// Helper functions pour sqlite3 (API asynchrone)
const getUserByUsername = (username, callback) => {
  db.get('SELECT id, username, password_hash FROM users WHERE username = ?', [username], callback);
};

const insertCategory = (name, userId, callback) => {
  db.run('INSERT INTO categories (name, user_id) VALUES (?, ?)', [name, userId], callback);
};

const updateCategory = (id, name, userId, callback) => {
  db.run('UPDATE categories SET name = ? WHERE id = ? AND user_id = ?', [name, id, userId], callback);
};

const deleteCategory = (id, userId, callback) => {
  db.run('DELETE FROM categories WHERE id = ? AND user_id = ?', [id, userId], callback);
};

const getCategories = (userId, callback) => {
  db.all('SELECT id, name FROM categories WHERE user_id = ? ORDER BY name', [userId], callback);
};

const insertTransaction = (amount, type, categoryId, description, dateIso, userId, callback) => {
  db.run('INSERT INTO transactions (amount, type, category_id, description, date_iso, user_id) VALUES (?, ?, ?, ?, ?, ?)',
    [amount, type, categoryId, description, dateIso, userId], callback);
};

const getTransactions = (userId, callback) => {
  db.all(`SELECT t.id, t.amount, t.type, t.description, t.date_iso, c.id as category_id, c.name as category_name
    FROM transactions t LEFT JOIN categories c ON t.category_id = c.id
    WHERE t.user_id = ? ORDER BY date_iso DESC`, [userId], callback);
};

const deleteTransaction = (id, userId, callback) => {
  db.run('DELETE FROM transactions WHERE id = ? AND user_id = ?', [id, userId], callback);
};

const getBalance = (userId, callback) => {
  db.get(`SELECT
    COALESCE(SUM(CASE WHEN type='in' THEN amount ELSE 0 END),0) as total_in,
    COALESCE(SUM(CASE WHEN type='out' THEN amount ELSE 0 END),0) as total_out
  FROM transactions WHERE user_id = ?`, [userId], callback);
};

// Routes d'authentification
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;
  if (!username || !password) {
    return res.status(400).json({ error: 'Nom d\'utilisateur et mot de passe requis' });
  }

  getUserByUsername(username, (err, user) => {
    if (err) {
      console.error('Erreur lors de la recherche de l\'utilisateur:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    if (!user) {
      return res.status(401).json({ error: 'Identifiants invalides' });
    }

    // Vérification simple du mot de passe (à améliorer avec bcrypt)
    if (password !== 'password') { // Mot de passe temporaire
      return res.status(401).json({ error: 'Identifiants invalides' });
    }

    // Créer un token simple (à remplacer par JWT en production)
    const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
    sessions.set(token, { id: user.id, username: user.username });

    res.json({ success: true, username: user.username, token });
  });
});

app.post('/api/logout', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token) {
    sessions.delete(token);
  }
  res.json({ success: true });
});

app.get('/api/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token || !sessions.has(token)) {
    return res.status(401).json({ error: 'Non authentifié' });
  }
  const user = sessions.get(token);
  res.json({
    authenticated: true,
    username: user.username,
    userId: user.id
  });
});

// Routes protégées
app.get('/api/categories', requireAuth, (req, res) => {
  getCategories(req.user.id, (err, rows) => {
    if (err) {
      console.error('Erreur lors de la récupération des catégories:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.json(rows || []);
  });
});

app.post('/api/categories', requireAuth, (req, res) => {
  const { name } = req.body;
  if (!name || !name.trim()) return res.status(400).json({ error: 'Nom requis' });

  insertCategory(name.trim(), req.user.id, function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT') {
        return res.status(400).json({ error: 'Cette catégorie existe déjà' });
      }
      console.error('Erreur lors de l\'insertion de la catégorie:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.status(201).json({ id: this.lastID, ok: true });
  });
});

app.put('/api/categories/:id', requireAuth, (req, res) => {
  const { name } = req.body;
  const id = Number(req.params.id);

  if (!name || !name.trim()) return res.status(400).json({ error: 'Nom requis' });
  if (isNaN(id)) return res.status(400).json({ error: 'ID invalide' });

  updateCategory(id, name.trim(), req.user.id, function(err) {
    if (err) {
      console.error('Erreur lors de la mise à jour de la catégorie:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Catégorie non trouvée' });
    }
    res.json({ ok: true });
  });
});

app.delete('/api/categories/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) return res.status(400).json({ error: 'ID invalide' });

  deleteCategory(id, req.user.id, function(err) {
    if (err) {
      console.error('Erreur lors de la suppression de la catégorie:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Catégorie non trouvée' });
    }
    res.json({ ok: true });
  });
});

// Route pour initialiser les catégories par défaut
app.post('/api/categories/init-defaults', requireAuth, (req, res) => {
  const defaultCategories = [
    '🏠 Logement',
    '🚗 Transport',
    '🍽️ Alimentation',
    '📱 Téléphone',
    '💡 Électricité',
    '💧 Eau',
    '🌐 Internet',
    '🏥 Santé',
    '👕 Vêtements',
    '🎬 Loisirs',
    '📚 Éducation',
    '🎁 Cadeaux',
    '💰 Épargne',
    '🏪 Courses',
    '⛽ Carburant',
    '🔧 Réparations',
    '📄 Assurances',
    '🏦 Banque',
    '💼 Travail',
    '🎯 Autres'
  ];

  let completed = 0;
  let errors = 0;

  defaultCategories.forEach(categoryName => {
    insertCategory(categoryName, req.user.id, (err) => {
      completed++;
      if (err) {
        errors++;
        console.error('Erreur lors de la création de la catégorie:', categoryName, err);
      }

      // Quand toutes les catégories ont été traitées
      if (completed === defaultCategories.length) {
        const created = defaultCategories.length - errors;
        res.json({
          ok: true,
          message: `${created} catégories créées avec succès`,
          created: created,
          errors: errors
        });
      }
    });
  });
});

app.get('/api/transactions', requireAuth, (req, res) => {
  getTransactions(req.user.id, (err, rows) => {
    if (err) {
      console.error('Erreur lors de la récupération des transactions:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.json(rows || []);
  });
});

app.post('/api/transactions', requireAuth, (req, res) => {
  const { amount, type, category_id, description, date_iso } = req.body;
  if (typeof amount !== 'number' || !['in','out'].includes(type)) {
    return res.status(400).json({ error: 'Données invalides' });
  }

  const dateISO = date_iso || new Date().toISOString();
  const catId = category_id || null;

  insertTransaction(amount, type, catId, description || null, dateISO, req.user.id, function(err) {
    if (err) {
      console.error('Erreur lors de l\'insertion de la transaction:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.status(201).json({ id: this.lastID });
  });
});

app.delete('/api/transactions/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }

  deleteTransaction(id, req.user.id, (err) => {
    if (err) {
      console.error('Erreur lors de la suppression de la transaction:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.json({ ok: true });
  });
});

app.get('/api/balance', requireAuth, (req, res) => {
  getBalance(req.user.id, (err, row) => {
    if (err) {
      console.error('Erreur lors du calcul du solde:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    const balance = {
      total_in: row?.total_in || 0,
      total_out: row?.total_out || 0,
      balance: (row?.total_in || 0) - (row?.total_out || 0)
    };

    res.json(balance);
  });
});

const PORT = process.env.PORT || 3333;
app.listen(PORT, () => console.log(`Server started on http://localhost:${PORT}`));