#!/bin/bash

echo "🔄 Redémarrage du serveur backend..."

# Arrêter le serveur s'il est en cours d'exécution
echo "📋 Recherche des processus Node.js en cours..."
pkill -f "node index.js" 2>/dev/null || echo "Aucun serveur à arrêter"

# Attendre un peu pour que le processus se termine
sleep 2

# Aller dans le dossier serveur
cd server

echo "🚀 Démarrage du serveur backend..."
# Démarrer le serveur en arrière-plan
npm start &

# Attendre que le serveur démarre
sleep 3

echo "✅ Serveur backend redémarré !"
echo "🌐 Serveur disponible sur : http://localhost:3333"
echo ""
echo "📊 Pour voir les logs du serveur : tail -f server.log"
echo "🛑 Pour arrêter le serveur : pkill -f 'node index.js'"
